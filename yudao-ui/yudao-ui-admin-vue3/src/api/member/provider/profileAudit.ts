import request from '@/config/axios'
import type { Dayjs } from 'dayjs'

/** 服务商资料审核记录信息 */
export interface ProviderProfileAuditVO {
  id: number // 主键
  userId?: number // 用户编号
  mobile?: string // 手机号
  providerType?: number // 服务商类型
  avatarUrl: string // 头像URL
  brandLogoUrl: string // 品牌LOGO URL
  contactWechat: string // 对外微信号
  contactMobile: string // 对外手机号
  experienceDesc: string // 经验描述
  serviceCategoryIds: number[] // 服务类目编号数组
  description: string // 服务描述
  serviceRegionCodes: number[] // 服务城市编码，逗号分隔
  provinceCode: number // 所在省编码
  cityCode: number // 所在市编码
  districtCode: number // 所在区编码
  addressDetail: string // 详细地址
  promotionPicUrls: string // 宣传图片URL，逗号分隔
  isPlatformProvider: boolean // 是否平台服务商：0=否，1=是
  auditStatus?: number // 审核状态：0：未提交，1：待审核，2：审核通过，3：审核拒绝
  auditTime: string | Dayjs // 审核时间
  auditReason: string // 审核失败原因
  auditBy: string // 审核人
  systemAuditDetail: string // 系统自动内容安全审核详情
}

// 处理审核 VO
export interface ProviderProfileAuditHandleReqVO {
  id: number // 商家编号
  auditStatus: number // 审核状态：0：待审核，1：审核通过，2：审核拒绝
  auditReason: string // 审核失败原因
}

// 服务商资料审核记录 API
export const ProviderProfileAuditApi = {
  // 查询服务商资料审核记录分页
  getProviderProfileAuditPage: async (params: any) => {
    return await request.get({ url: `/member/provider-profile-audit/page`, params })
  },

  // 查询服务商资料审核记录详情
  getProviderProfileAudit: async (id: number) => {
    return await request.get({ url: `/member/provider-profile-audit/get?id=` + id })
  },

  // 新增服务商资料审核记录
  createProviderProfileAudit: async (data: ProviderProfileAuditVO) => {
    return await request.post({ url: `/member/provider-profile-audit/create`, data })
  },

  // 修改服务商资料审核记录
  updateProviderProfileAudit: async (data: ProviderProfileAuditVO) => {
    return await request.put({ url: `/member/provider-profile-audit/update`, data })
  },

  // 删除服务商资料审核记录
  deleteProviderProfileAudit: async (id: number) => {
    return await request.delete({ url: `/member/provider-profile-audit/delete?id=` + id })
  },

  /** 批量删除服务商资料审核记录 */
  deleteProviderProfileAuditList: async (ids: number[]) => {
    return await request.delete({ url: `/member/provider-profile-audit/delete-list?ids=${ids.join(',')}` })
  },

  // 审核服务商资料
  handleProfileAudit: async (data: ProviderProfileAuditHandleReqVO) => {
    return await request.put({ url: `/member/provider-profile-audit/handle`, data })
  }
}
