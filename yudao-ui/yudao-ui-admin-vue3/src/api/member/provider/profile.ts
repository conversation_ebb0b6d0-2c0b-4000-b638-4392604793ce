import request from '@/config/axios'
import type { Dayjs } from 'dayjs'

/** 服务商资料信息 */
export interface ProviderProfileVO {
  id: number // 主键
  userId?: number // 用户编号
  mobile?: string // 手机号
  providerType?: number // 服务商类型
  avatarUrl: string // 头像URL
  brandLogoUrl: string // 品牌LOGO URL 
  contactWechat: string // 对外微信号
  contactMobile: string // 对外手机号
  experienceDesc: string // 经验描述
  serviceCategoryIds: string // 服务类目编号数组
  description: string // 服务描述
  serviceRegionCodes: string // 服务城市编码，逗号分隔
  provinceCode: number // 所在省编码
  cityCode: number // 所在市编码
  districtCode: number // 所在区编码
  addressDetail: string // 详细地址
  promotionPicUrls: string // 宣传图片URL，逗号分隔
  isPlatformProvider: boolean // 是否平台服务商：0=否，1=是
  profileStatus?: number // 资料状态：0=未提交，1=可用
}

// 服务商资料 API
export const ProviderProfileApi = {
  // 查询服务商资料分页
  getProviderProfilePage: async (params: any) => {
    return await request.get({ url: `/member/provider-profile/page`, params })
  },

  // 查询服务商资料详情
  getProviderProfile: async (id: number) => {
    return await request.get({ url: `/member/provider-profile/get?id=` + id })
  },

  // 新增服务商资料
  createProviderProfile: async (data: ProviderProfileVO) => {
    return await request.post({ url: `/member/provider-profile/create`, data })
  },

  // 修改服务商资料
  updateProviderProfile: async (data: ProviderProfileVO) => {
    return await request.put({ url: `/member/provider-profile/update`, data })
  },

  // 删除服务商资料
  deleteProviderProfile: async (id: number) => {
    return await request.delete({ url: `/member/provider-profile/delete?id=` + id })
  },

  /** 批量删除服务商资料 */
  deleteProviderProfileList: async (ids: number[]) => {
    return await request.delete({ url: `/member/provider-profile/delete-list?ids=${ids.join(',')}` })
  },

  // 导出服务商资料 Excel
  exportProviderProfile: async (params) => {
    return await request.download({ url: `/member/provider-profile/export-excel`, params })
  }
}
