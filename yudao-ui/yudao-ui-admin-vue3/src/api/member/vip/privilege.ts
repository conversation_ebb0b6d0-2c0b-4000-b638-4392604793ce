import request from '@/config/axios'

// 会员套餐权益 VO
export interface VipPrivilegeVO {
  id: number // 主键ID
  packageId: number // 套餐编号
  privilegeCode: string // 权限编码（若仅用于展示则可为 NULL）
  title: string // 权益标题
  description: string // 详细描述
  iconUrl: string // 图标URL
  sort: number // 排序字段
  visible: boolean // 是否前端展示
}

// 会员套餐权益 API
export const VipPrivilegeApi = {
  // 查询会员套餐权益分页
  getVipPrivilegePage: async (params: any) => {
    return await request.get({ url: `/member/vip-privilege/page`, params })
  },

  // 查询会员套餐权益详情
  getVipPrivilege: async (id: number) => {
    return await request.get({ url: `/member/vip-privilege/get?id=` + id })
  },

  // 新增会员套餐权益
  createVipPrivilege: async (data: VipPrivilegeVO) => {
    return await request.post({ url: `/member/vip-privilege/create`, data })
  },

  // 修改会员套餐权益
  updateVipPrivilege: async (data: VipPrivilegeVO) => {
    return await request.put({ url: `/member/vip-privilege/update`, data })
  },

  // 删除会员套餐权益
  deleteVipPrivilege: async (id: number) => {
    return await request.delete({ url: `/member/vip-privilege/delete?id=` + id })
  },

  // 导出会员套餐权益 Excel
  exportVipPrivilege: async (params) => {
    return await request.download({ url: `/member/vip-privilege/export-excel`, params })
  }
}