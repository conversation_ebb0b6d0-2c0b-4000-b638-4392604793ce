import request from '@/config/axios'

// 会员套餐 VO
export interface VipPackageVO {
  id: number // 套餐编号
  name: string // 套餐名称
  code: string // 套餐编码
  description: string // 套餐描述
  price: number // 价格，单位：分
  originalPrice: number // 原价，单位：分
  durationDays: number // 有效期，单位：天
  level: number // 套餐等级
  promotionLabel: string // 促销文案标签
  sort: number // 排序值
  status: number // 状态
}

// 会员套餐 API
export const VipPackageApi = {
  // 查询会员套餐分页
  getVipPackagePage: async (params: any) => {
    return await request.get({ url: `/member/vip-package/page`, params })
  },

  // 查询会员套餐详情
  getVipPackage: async (id: number) => {
    return await request.get({ url: `/member/vip-package/get?id=` + id })
  },

  // 新增会员套餐
  createVipPackage: async (data: VipPackageVO) => {
    return await request.post({ url: `/member/vip-package/create`, data })
  },

  // 修改会员套餐
  updateVipPackage: async (data: VipPackageVO) => {
    return await request.put({ url: `/member/vip-package/update`, data })
  },

  // 删除会员套餐
  deleteVipPackage: async (id: number) => {
    return await request.delete({ url: `/member/vip-package/delete?id=` + id })
  },

  // 导出会员套餐 Excel
  exportVipPackage: async (params) => {
    return await request.download({ url: `/member/vip-package/export-excel`, params })
  }
}