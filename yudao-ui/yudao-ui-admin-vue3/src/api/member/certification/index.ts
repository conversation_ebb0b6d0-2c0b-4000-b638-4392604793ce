import request from '@/config/axios'

// 会员实名认证 VO
export interface CertificationVO {
  id: number // 认证编号
  userId: number // 用户编号
  certType: number // 认证类型：1：个人认证，2：企业认证
  realName: string // 认证姓名
  mobile: string // 认证手机号
  idCardNumber: string // 身份证号码
  idCardFrontUrl: string // 身份证正面照片URL
  idCardBackUrl: string // 身份证背面照片URL
  corpName: string // 企业名称
  corpLicenseNumber: string // 统一社会信用代码
  corpLicenseUrl: string // 营业执照扫描件URL
  corpContactName: string // 企业联系人姓名
  auditStatus: number // 审核状态：0：待审核，1：审核通过，2：审核拒绝
  auditTime: Date // 审核时间
  auditReason: string // 审核失败原因
  validStartTime: Date // 认证有效期开始时间
  validEndTime: Date // 认证有效期结束时间
}

// 审核会员实名认证 VO
export interface CertificationAuditVO {
  id: number // 认证编号
  auditStatus: number // 审核状态：0：待审核，1：审核通过，2：审核拒绝
  auditReason: string // 审核失败原因
}

// 会员实名认证 API
export const CertificationApi = {
  // 查询会员实名认证分页
  getCertificationPage: async (params: any) => {
    return await request.get({ url: `/member/certification/page`, params })
  },

  // 查询会员实名认证详情
  getCertification: async (id: number) => {
    return await request.get({ url: `/member/certification/get?id=` + id })
  },

  // 新增会员实名认证
  createCertification: async (data: CertificationVO) => {
    return await request.post({ url: `/member/certification/create`, data })
  },

  // 修改会员实名认证
  updateCertification: async (data: CertificationVO) => {
    return await request.put({ url: `/member/certification/update`, data })
  },

  // 删除会员实名认证
  deleteCertification: async (id: number) => {
    return await request.delete({ url: `/member/certification/delete?id=` + id })
  },

  // 审核会员实名认证
  auditCertification: async (data: CertificationAuditVO) => {
    return await request.put({ url: `/member/certification/audit`, data })
  },

  // 导出会员实名认证 Excel
  exportCertification: async (params) => {
    return await request.download({ url: `/member/certification/export-excel`, params })
  }
}
