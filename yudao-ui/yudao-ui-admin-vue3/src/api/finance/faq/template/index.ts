import request from '@/config/axios'
import type { Dayjs } from 'dayjs'

/** FAQ 模板信息 */
export interface FaqTemplateVO {
  id: number // 主键
  name?: string // 模板名称
  sceneType: number // 业务场景
  sceneBizId: number // 业务场景下的业务 ID
  remark: string // 备注
  status?: boolean // 状态
}

// FAQ 模板 API
export const FaqTemplateApi = {
  // 查询FAQ 模板分页
  getFaqTemplatePage: async (params: any) => {
    return await request.get({ url: `/faq/template/page`, params })
  },

  // 查询FAQ 模板详情
  getFaqTemplate: async (id: number) => {
    return await request.get({ url: `/faq/template/get?id=` + id })
  },

  // 获取FAQ 模板精简信息列表
  getFaqTemplateSimpleList: async () => {
    return request.get({ url: '/faq/template/list-all-simple' })
  },

  // 新增FAQ 模板
  createFaqTemplate: async (data: FaqTemplateVO) => {
    return await request.post({ url: `/faq/template/create`, data })
  },

  // 修改FAQ 模板
  updateFaqTemplate: async (data: FaqTemplateVO) => {
    return await request.put({ url: `/faq/template/update`, data })
  },

  // 删除FAQ 模板
  deleteFaqTemplate: async (id: number) => {
    return await request.delete({ url: `/faq/template/delete?id=` + id })
  },

  /** 批量删除FAQ 模板 */
  deleteFaqTemplateList: async (ids: number[]) => {
    return await request.delete({ url: `/faq/template/delete-list?ids=${ids.join(',')}` })
  },

  // 导出FAQ 模板 Excel
  exportFaqTemplate: async (params) => {
    return await request.download({ url: `/faq/template/export-excel`, params })
  }
}
