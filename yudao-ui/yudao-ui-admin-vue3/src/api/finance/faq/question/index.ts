import request from '@/config/axios'
import type { Dayjs } from 'dayjs'

/** FAQ 选项信息 */
export interface FaqOptionVO {
  id: number // 主键
  questionId?: number // 所属题目 ID
  optionType?: number // 选项类型
  optionValue: string // 选项值
  optionText?: string // 选项内容
  nextQuestionId: number // 跳转题目 ID，NULL 表示到此结束
  sortOrder?: number // 同题目内选项显示顺序
}

/** FAQ 题目信息 */
export interface FaqQuestionVO {
  id: number // 主键
  templateId?: number // 所属模板 ID
  questionText?: string // 问题正文
  sortOrder?: number // 题目顺序
  faqoptions?: FaqOptionVO[]
}

// FAQ 题目 API
export const FaqQuestionApi = {
  // 查询FAQ 题目分页
  getFaqQuestionPage: async (params: any) => {
    return await request.get({ url: `/faq/question/page`, params })
  },

  // 查询FAQ 题目详情
  getFaqQuestion: async (id: number) => {
    return await request.get({ url: `/faq/question/get?id=` + id })
  },

  // 获取题目精简信息列表
  getFaqQuestionSimpleList: async () => {
    return request.get({ url: '/faq/question/list-all-simple'})
  },

  // 新增FAQ 题目
  createFaqQuestion: async (data: FaqQuestionVO) => {
    return await request.post({ url: `/faq/question/create`, data })
  },

  // 修改FAQ 题目
  updateFaqQuestion: async (data: FaqQuestionVO) => {
    return await request.put({ url: `/faq/question/update`, data })
  },

  // 删除FAQ 题目
  deleteFaqQuestion: async (id: number) => {
    return await request.delete({ url: `/faq/question/delete?id=` + id })
  },

  /** 批量删除FAQ 题目 */
  deleteFaqQuestionList: async (ids: number[]) => {
    return await request.delete({ url: `/faq/question/delete-list?ids=${ids.join(',')}` })
  },

  // 导出FAQ 题目 Excel
  exportFaqQuestion: async (params) => {
    return await request.download({ url: `/faq/question/export-excel`, params })
  },

  // ==================== 子表（FAQ 选项） ====================

  // 获得FAQ 选项列表
  getFaqOptionListByQuestionId: async (questionId) => {
    return await request.get({
      url: `/faq/question/faq-option/list-by-question-id?questionId=` + questionId
    })
  }
}
