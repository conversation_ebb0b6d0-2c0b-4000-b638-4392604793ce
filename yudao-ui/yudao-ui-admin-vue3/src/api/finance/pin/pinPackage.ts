import request from '@/config/axios'

/** 置顶套餐信息 */
export interface PinPackageVO {
  id: number // 主键 ID
  name?: string // 套餐名称
  durationDays?: number // 置顶时长（单位：天）
  price?: number // 套餐价格（单位：分）
  sort: number // 排序值
  status?: number // 套餐状态
  remark: string // 套餐备注
}

// 置顶套餐 API
export const PinPackageApi = {
  // 查询置顶套餐分页
  getPinPackagePage: async (params: any) => {
    return await request.get({ url: `/pin/package/page`, params })
  },

  // 获得置顶套餐精简列表
  getPinPackageSimpleList: async () => {
    return request.get({ url: '/pin/package/list-all-simple' })
  },

  // 查询置顶套餐详情
  getPinPackage: async (id: number) => {
    return await request.get({ url: `/pin/package/get?id=` + id })
  },

  // 新增置顶套餐
  createPinPackage: async (data: PinPackageVO) => {
    return await request.post({ url: `/pin/package/create`, data })
  },

  // 修改置顶套餐
  updatePinPackage: async (data: PinPackageVO) => {
    return await request.put({ url: `/pin/package/update`, data })
  },

  // 删除置顶套餐
  deletePinPackage: async (id: number) => {
    return await request.delete({ url: `/pin/package/delete?id=` + id })
  },

  /** 批量删除置顶套餐 */
  deletePinPackageList: async (ids: number[]) => {
    return await request.delete({ url: `/pin/package/delete-list?ids=${ids.join(',')}` })
  }
}
