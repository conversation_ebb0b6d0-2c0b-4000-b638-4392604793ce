import request from '@/config/axios'
import type { Dayjs } from 'dayjs'

/** 置顶对象记录信息 */
export interface PinItemVO {
  id: number // 主键 ID
  pinType?: number // 置顶类型
  bizType?: number // 置顶对象类型
  bizId?: number // 置顶对象 ID
  bizName?: number // 置顶对象名称
  pinCityCodes?: number[] // 置顶城市编号数组
  packageId?: number // 置顶套餐 ID
  startTime?: string | Dayjs // 置顶开始时间
  endTime?: string | Dayjs // 置顶结束时间
  sort: number // 排序
  status?: number // 状态
}

// 置顶对象记录 API
export const PinItemApi = {
  // 查询置顶对象记录分页
  getPinItemPage: async (params: any) => {
    return await request.get({ url: `/pin/item/page`, params })
  },

  // 查询置顶对象记录详情
  getPinItem: async (id: number) => {
    return await request.get({ url: `/pin/item/get?id=` + id })
  },

  // 新增置顶对象记录
  createPinItem: async (data: any) => {
    return await request.post({ url: `/pin/item/create`, data })
  },

  // 更新置顶对象记录 status
  updateStatus: async (data: { id: number; status: number }) => {
    return request.put({ url: '/pin/item/update-status', data })
  },

  // 删除置顶对象记录
  deletePinItem: async (id: number) => {
    return await request.delete({ url: `/pin/item/delete?id=` + id })
  },

  /** 批量删除置顶对象记录 */
  deletePinItemList: async (ids: number[]) => {
    return await request.delete({ url: `/pin/item/delete-list?ids=${ids.join(',')}` })
  }
}
