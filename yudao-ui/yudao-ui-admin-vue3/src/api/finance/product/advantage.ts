import request from '@/config/axios'
import type { Dayjs } from 'dayjs'

/** 产品优势标签信息 */
export interface ProductAdvantage {
  id: number // 主键编号
  name?: string // 标签名称
  sort: number // 排序值
}

// 产品优势标签 API
export const ProductAdvantageApi = {
  // 查询产品优势标签分页
  getProductAdvantagePage: async (params: any) => {
    return await request.get({ url: `/product/advantage/page`, params })
  },

  // 查询产品优势标签详情
  getProductAdvantage: async (id: number) => {
    return await request.get({ url: `/product/advantage/get?id=` + id })
  },

  // 查询产品优势标签列表
  getProductAdvantageList: async () => {
    return await request.get({ url: `/product/advantage/list` })
  },

  // 新增产品优势标签
  createProductAdvantage: async (data: ProductAdvantage) => {
    return await request.post({ url: `/product/advantage/create`, data })
  },

  // 修改产品优势标签
  updateProductAdvantage: async (data: ProductAdvantage) => {
    return await request.put({ url: `/product/advantage/update`, data })
  },

  // 删除产品优势标签
  deleteProductAdvantage: async (id: number) => {
    return await request.delete({ url: `/product/advantage/delete?id=` + id })
  },

  /** 批量删除产品优势标签 */
  deleteProductAdvantageList: async (ids: number[]) => {
    return await request.delete({ url: `/product/advantage/delete-list?ids=${ids.join(',')}` })
  }
}
