import request from '@/config/axios'

// 产品spu VO
export interface SpuVO {
  id: number // 产品编号
  name: string // 产品名称
  keyword: string // 关键字
  introduction: string // 产品简介
  description: string // 产品详情
  providerUserId: number // 服务商用户编号
  serviceCategoryId: number // 服务类型编号
  serviceAreaIds: number // 服务区域编码，以逗号分隔 
  advantageTags: number // 产品优势标签，以逗号分隔
  picUrl: string // 产品封面图
  sliderPicUrls: string // 产品轮播图地址
  sort: number // 排序字段
  status: number // 产品状态: 1 上架（开启） 0 下架（禁用） -1 回收
  specType: boolean // 规格类型：0 单规格 1 多规格
  price: number // 产品价格，单位使用：分
  marketPrice: number // 市场价，单位使用：分 
  costPrice: number // 成本价，单位： 分
  stock: number // 库存
  salesCount: number // 产品销量
  virtualSalesCount: number // 虚拟销量
  browseCount: number // 产品点击量
}

// 产品spu API
export const SpuApi = {
  // 查询产品spu分页
  getSpuPage: async (params: any) => {
    return await request.get({ url: `/product/spu/page`, params })
  },

  // 查询产品spu详情
  getSpu: async (id: number) => {
    return await request.get({ url: `/product/spu/get?id=` + id })
  },

  // 新增产品spu
  createSpu: async (data: SpuVO) => {
    return await request.post({ url: `/product/spu/create`, data })
  },

  // 修改产品spu
  updateSpu: async (data: SpuVO) => {
    return await request.put({ url: `/product/spu/update`, data })
  },

  // 更新产品 Spu status
  updateStatus: async (data: { id: number; status: number }) => {
    return request.put({ url: '/product/spu/update-status', data })
  },

  // 获得商品 Spu 详情列表
  getSpuDetailList: async (ids: number[]) => {
    return request.get({ url: `/product/spu/list?spuIds=${ids}` })
  },

  // 获得商品 SPU 精简列表
  getSpuSimpleList: async () => {
    return request.get({ url: '/product/spu/list-all-simple' })
  },

  // 删除产品spu
  deleteSpu: async (id: number) => {
    return await request.delete({ url: `/product/spu/delete?id=` + id })
  }
}
