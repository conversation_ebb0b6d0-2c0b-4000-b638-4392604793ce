import request from '@/config/axios'

// 产品SPU审核 VO
export interface SpuAuditVO {
  id: number // 产品编号
  name: string // 产品名称
  keyword: string // 关键字
  introduction: string // 产品简介
  description: string // 产品详情
  providerUserId: number // 服务商用户编号
  providerUserMobile: string // 服务商用户手机
  serviceCategoryId: number // 服务类型编号
  serviceCategoryName: number // 服务类型名称
  serviceAreaCode: number // 服务区域编码
  serviceAreaName: string // 服务区域名称
  advantageTags: string // 产品优势标签，以逗号分隔
  picUrl: string // 产品封面图
  sliderPicUrls: string[] // 产品轮播图地址
  price: number // 产品价格，单位使用：分
  marketPrice: number // 市场价，单位使用：分
  serviceProcessNames: string[] // 产品服务流程
  auditStatus: number // 审核状态：0待审，1通过，2拒绝
  auditReason: string // 审核不通过原因
  auditTime: Date // 审核时间
  auditBy: number // 审核人ID
}

// 审批产品SPU审核 VO
export interface SpuAuditDecisionVO {
  id: number // 审核记录编号
  auditStatus: number // 审核状态：0：待审核，1：审核通过，2：审核拒绝
  auditReason: string // 审核失败原因
}

// 产品SPU审核 API
export const SpuAuditApi = {
  // 查询产品SPU审核分页
  getSpuAuditPage: async (params: any) => {
    return await request.get({ url: `/product/spu-audit/page`, params })
  },

  // 查询产品SPU审核详情
  getSpuAuditDetail: async (id: number) => {
    return await request.get({ url: `/product/spu-audit/get-detail?id=` + id })
  },

  // 审批产品SPU审核记录
  decisionSpuAudit: async (data: SpuAuditDecisionVO) => {
    return await request.put({ url: `/product/spu-audit/decision`, data })
  },

  // 删除产品SPU审核
  deleteSpuAudit: async (id: number) => {
    return await request.delete({ url: `/product/spu-audit/delete?id=` + id })
  }
}
