import request from '@/config/axios'
import type { Dayjs } from 'dayjs';

/** 产品服务流程信息 */
export interface ProductServiceProcess {
          id: number; // 主键编号
          name?: string; // 步骤名称
          serviceCategoryId?: number; // 服务类型编号
          sort: number; // 排序值
          status?: number; // 状态
  }

// 产品服务流程 API
export const ProductServiceProcessApi = {
  // 查询产品服务流程分页
  getProductServiceProcessPage: async (params: any) => {
    return await request.get({ url: `/product/service-process/page`, params })
  },

  // 查询产品服务流程详情
  getProductServiceProcess: async (id: number) => {
    return await request.get({ url: `/product/service-process/get?id=` + id })
  },

  // 查询产品服务流程详情列表
  getProductServiceProcessList: async () => {
    return await request.get({ url: `/product/service-process/list`})
  },

  // 查询指定分类的产品服务流程列表
  getProductServiceProcessListByCategoryId: async (serviceCategoryId: number) => {
    return await request.get({ url: `/product/service-process/list-by-category-id?serviceCategoryId=` + serviceCategoryId })
  },

  // 新增产品服务流程
  createProductServiceProcess: async (data: ProductServiceProcess) => {
    return await request.post({ url: `/product/service-process/create`, data })
  },

  // 修改产品服务流程
  updateProductServiceProcess: async (data: ProductServiceProcess) => {
    return await request.put({ url: `/product/service-process/update`, data })
  },

  // 删除产品服务流程
  deleteProductServiceProcess: async (id: number) => {
    return await request.delete({ url: `/product/service-process/delete?id=` + id })
  },

  /** 批量删除产品服务流程 */
  deleteProductServiceProcessList: async (ids: number[]) => {
    return await request.delete({ url: `/product/service-process/delete-list?ids=${ids.join(',')}` })
  },
}