import request from '@/config/axios'

/** 产品配置信息 */
export interface ProductConfigVO {
  id: number // 主键 ID
  publishLimitCount: number // 发布产品次数上限（单个用户最多可发布的产品数量）
  publishAuditMode: number // 发布产品的审核方式（0=无需审核，1=敏感词，2=内容安全，3=人工审核
}

// 产品配置 API
export const ProductConfigApi = {

 // 获取产品配置
  getProductConfig: async () => {
    return await request.get({ url: `/product/config/get` })
  },

  // 保存产品配置
  saveProductConfig: async (data: ProductConfigVO) => {
    return await request.put({ url: `/product/config/save`, data })
  }
}
