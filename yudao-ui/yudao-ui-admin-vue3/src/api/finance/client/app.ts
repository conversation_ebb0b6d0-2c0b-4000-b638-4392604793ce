import request from '@/config/axios'
import type { Dayjs } from 'dayjs';

/** 客户端应用信息 */
export interface ClientApp {
          id: number; // 主键
          appCode?: string; // 应用编码
          appName?: string; // 应用名称
          logoUrl: string; // 应用图标
          description: string; // 应用描述
          sort?: number; // 排序
          remark: string; // 备注
          status?: number; // 状态
  }

// 客户端应用 API
export const ClientAppApi = {
  // 查询客户端应用分页
  getClientAppPage: async (params: any) => {
    return await request.get({ url: `/client/app/page`, params })
  },

  // 查询客户端应用详情
  getClientApp: async (id: number) => {
    return await request.get({ url: `/client/app/get?id=` + id })
  },

  // 新增客户端应用
  createClientApp: async (data: ClientApp) => {
    return await request.post({ url: `/client/app/create`, data })
  },

  // 修改客户端应用
  updateClientApp: async (data: ClientApp) => {
    return await request.put({ url: `/client/app/update`, data })
  },

  // 删除客户端应用
  deleteClientApp: async (id: number) => {
    return await request.delete({ url: `/client/app/delete?id=` + id })
  },

  // 获取客户端应用列表
  getClientAppList: async () => {
    return await request.get({ url: `/client/app/list` })
  },

  // 根据应用编码获取应用
  getClientAppByAppCode: async (appCode: string) => {
    return await request.get({ url: `/client/app/get-by-code?appCode=` + appCode })
  },

  // 导出客户端应用 Excel
  exportClientApp: async (params) => {
    return await request.download({ url: `/client/app/export-excel`, params })
  }
}
