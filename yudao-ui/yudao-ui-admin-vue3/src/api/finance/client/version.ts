import request from '@/config/axios'

/** 客户端应用版本信息 */
export interface ClientAppVersion {
  id?: number // 主键
  appId: number // 客户端应用编号
  terminalId: number // 终端编号
  terminalType: number // 终端类型
  terminalName: string // 终端名称
  versionCode: number // 版本号
  versionName: string // 版本名称
  forceUpdate?: boolean // 是否强制更新
  releaseNotes?: string // 更新说明
  downloadUrl?: string // 下载地址
  supportHotUpdate?: boolean // 是否支持热更新
  hotUpdateVersion?: string // 热更新版本
  hotUpdateCode?: number // 热更新版本号
  hotUpdateUrl?: string // 热更新地址
  status?: number // 状态
  publishTime?: Date // 发布时间
  createTime?: Date // 创建时间
}

// 客户端应用版本 API
export const ClientAppVersionApi = {
  // 查询客户端应用版本分页
  getClientAppVersionPage: async (params: any) => {
    return await request.get({ url: `/client/version/page`, params })
  },

  // 查询客户端应用版本详情
  getClientAppVersion: async (id: number) => {
    return await request.get({ url: `/client/version/get?id=` + id })
  },

  // 新增客户端应用版本
  createClientAppVersion: async (data: ClientAppVersion) => {
    return await request.post({ url: `/client/version/create`, data })
  },

  // 修改客户端应用版本
  updateClientAppVersion: async (data: ClientAppVersion) => {
    return await request.put({ url: `/client/version/update`, data })
  },

  // 删除客户端应用版本
  deleteClientAppVersion: async (id: number) => {
    return await request.delete({ url: `/client/version/delete?id=` + id })
  },

  // 更新版本状态 Status
  updateClientAppVersionStatus: async (data: { id: number; status: number }) => {
    return request.put({ url: '/client/version/update-status', data })
  },

  // 根据终端ID获取版本列表
  getVersionListByTerminalId: async (terminalId: number) => {
    return await request.get({ url: `/client/version/list-by-terminal?terminalId=` + terminalId })
  }
}
