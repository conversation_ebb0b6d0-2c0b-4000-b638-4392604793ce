import request from '@/config/axios'

/** 客户端应用配置信息 */
export interface ClientAppConfig {
  id?: number // 主键
  appId: number // 客户端应用编号
  configKey: string // 配置键
  configValue: string // 配置值
  valueType: string // 值类型
  sort?: number // 排序
  remark?: string // 备注
  status?: number // 状态
  createTime?: Date // 创建时间
}

// 客户端配置 API
export const ClientAppConfigApi = {
  // 获取客户端应用全局配置
  getClientAppGlobalConfig: async (appId: number) => {
    return await request.get({ url: `/client/app-config/get-global?appId=` + appId })
  },

  // 获客客户端应用终端配置
  getClientAppTerminalConfig: async (terminalId: number) => {
    return await request.get({ url: `/client/app-config/get-terminal?terminalId=${terminalId}` })
  },

  // 保存客户端应用终端配置
  saveClientAppTerminalConfig: async (terminalId: number, configMap: Record<string, any>) => {
    return await request.put({
      url: `/client/app-config/save-terminal?terminalId=` + terminalId,
      data: configMap
    })
  },

  // 保存客户端应用全局配置
  saveClientAppGlobalConfig: async (appId: number, configMap: Record<string, any>) => {
    return await request.put({
      url: `/client/app-config/save-global?appId=` + appId,
      data: configMap
    })
  }
}
