import request from '@/config/axios'

/** 客户端应用内容信息 */
export interface ClientAppContent {
  id?: number // 主键
  appId: number // 客户端应用编号
  contentType: string // 内容类型
  contentKey?: string // 内容键
  title: string // 标题
  content: string // 内容
  sort?: number // 排序
  remark?: string // 备注
  status?: number // 状态
  createTime?: Date // 创建时间
}

// 客户端应用内容 API
export const ClientAppContentApi = {
 
  // 新增客户端应用内容
  createClientAppContent: async (data: ClientAppContent) => {
    return await request.post({ url: `/client/app-content/create`, data })
  },

  // 修改客户端应用内容
  updateClientAppContent: async (data: ClientAppContent) => {
    return await request.put({ url: `/client/app-content/update`, data })
  },

  // 删除客户端应用内容
  deleteClientAppContent: async (id: number) => {
    return await request.delete({ url: `/client/app-content/delete?id=` + id })
  },

  // 根据应用ID获取内容列表
  getClientAppContentsByAppId: async (appId: number) => {
    return await request.get({ url: `/client/app-content/list-by-app?appId=` + appId })
  }
}
