import request from '@/config/axios'
import type { Dayjs } from 'dayjs';

/** 客户端应用终端信息 */
export interface ClientAppTerminal {
  id?: number // 主键
  appId: number // 客户端应用编号
  terminalType: number // 终端类型
  terminalName?: string // 终端名称
  displayName: string // 展示名称
  displayLogoUrl?: string // 展示图标
  sort?: number // 排序
  remark?: string // 备注
  status?: number // 状态
  createTime?: Date // 创建时间
}

// 客户端应用终端 API
export const ClientAppTerminalApi = {
  // 查询客户端应用终端分页
  getClientAppTerminalPage: async (params: any) => {
    return await request.get({ url: `/client/terminal/page`, params })
  },

  // 查询客户端应用终端详情
  getClientAppTerminal: async (id: number) => {
    return await request.get({ url: `/client/terminal/get?id=` + id })
  },

  // 新增客户端应用终端
  createClientAppTerminal: async (data: ClientAppTerminal) => {
    return await request.post({ url: `/client/terminal/create`, data })
  },

  // 修改客户端应用终端
  updateClientAppTerminal: async (data: ClientAppTerminal) => {
    return await request.put({ url: `/client/terminal/update`, data })
  },

  // 删除客户端应用终端
  deleteClientAppTerminal: async (id: number) => {
    return await request.delete({ url: `/client/terminal/delete?id=` + id })
  },

  // 根据应用ID获取终端列表
  getTerminalListByAppId: async (appId: number) => {
    return await request.get({ url: `/client/terminal/list-by-app?appId=` + appId })
  },

  // 根据应用ID和终端类型获取终端
  getTerminalByAppIdAndType: async (appId: number, terminalType: number) => {
    return await request.get({
      url: `/client/terminal/get-by-type`,
      params: { appId, terminalType }
    })
  },

  // 更新终端配置
  updateTerminalConfig: async (terminalId: number, configJson: string) => {
    return await request.put({
      url: `/client/terminal/update-config`,
      data: { terminalId, configJson }
    })
  }
}
