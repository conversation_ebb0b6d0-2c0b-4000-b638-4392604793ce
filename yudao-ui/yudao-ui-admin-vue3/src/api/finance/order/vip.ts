import request from '@/config/axios'
import type { Dayjs } from 'dayjs'

// 会员VIP订单 VO
export interface VipOrderVO {
  id: number // 订单编号
  userId?: number // 用户编号
  userIp?: string // 用户 IP
  packageId?: number // 套餐编号
  packageName: string // 套餐名称
  packageDays: number // 套餐有效期（单位：天）
  payPrice?: number // 实际支付金额
  packageLevel?: number // VIP 套餐等级
  payStatus?: boolean // 是否已支付：[0:未支付 1:已经支付过]
  payOrderId: number // 支付订单编号
  payChannelCode: string // 支付成功的支付渠道
  payTime: string | Dayjs // 订单支付时间
  payRefundId: number // 支付退款单编号
  refundPayPrice?: number // 退款支付金额
  refundTime: string | Dayjs // 退款时间
  refundStatus?: number // 退款状态
  appCode?: string // 订单来源应用
  terminal?: number // 订单来源终端
}

// 会员VIP订单 API
export const VipOrderApi = {
  // 查询会员VIP订单分页
  getVipOrderPage: async (params: any) => {
    return await request.get({ url: `/member/vip-order/page`, params })
  },

  // 查询会员VIP订单详情
  getVipOrder: async (id: number) => {
    return await request.get({ url: `/member/vip-order/get?id=` + id })
  },

  // 新增会员VIP订单
  createVipOrder: async (data: VipOrderVO) => {
    return await request.post({ url: `/member/vip-order/create`, data })
  },

  // 发起会员VIP订单退款
  refundVipOrder: async (id: number) => {
    return await request.put({ url: `/member/vip-order/refund?id=` + id })
  }
}
