import request from '@/config/axios'
import type { Dayjs } from 'dayjs'

/** 置顶订单信息 */
export interface PinOrder {
  id: number // 主键 ID
  userId?: number // 用户 ID
  payMode?: number // 支付模式
  pinType?: number // 置顶类型：1=同城，2=全国
  bizType?: number // 业务类型：1=产品，2=服务商
  userIp?: string // 用户 IP
  bizId?: number // 业务 ID（产品 ID或商家 ID）
  pinCityCodes?: string // 置顶的城市数组
  packageId?: number // 置顶套餐 ID
  payPrice?: number // 实际支付金额，单位：分
  packageName?: string // 置顶套餐名称
  payStatus?: boolean // 是否已支付：[0:未支付 1:已支付]
  packageDays: number // 置顶时长（单位：天）
  payOrderId: number // 支付订单编号
  payChannelCode: string // 支付渠道
  payTime: string | Dayjs // 支付时间
  payRefundId: number // 支付退款单编号
  refundPayPrice?: number // 退款金额，单位：分
  refundTime: string | Dayjs // 退款时间
  refundStatus?: number // 退款状态
  appCode?: string // 订单来源应用
  terminal?: number // 订单来源终端
}

// 置顶订单 API
export const PinOrderApi = {
  // 查询置顶订单分页
  getPinOrderPage: async (params: any) => {
    return await request.get({ url: `/pin/order/page`, params })
  },

  // 查询置顶订单详情
  getPinOrder: async (id: number) => {
    return await request.get({ url: `/pin/order/get?id=` + id })
  },

  // 发起置顶订单退款
  refundPinOrder: async (id: number) => {
    return await request.put({ url: `/pin/order/refund?id=` + id })
  },

  // 删除置顶订单
  deletePinOrder: async (id: number) => {
    return await request.delete({ url: `/pin/order/delete?id=` + id })
  },

  /** 批量删除置顶订单 */
  deletePinOrderList: async (ids: number[]) => {
    return await request.delete({ url: `/pin/order/delete-list?ids=${ids.join(',')}` })
  }
}
