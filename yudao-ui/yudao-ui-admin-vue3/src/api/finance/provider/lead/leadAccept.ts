import request from '@/config/axios'
import type { Dayjs } from 'dayjs'

/** 服务商接单记录信息 */
export interface ProviderLeadAccept {
  id: number // 主键ID
  leadId?: number // 线索ID
  demandHallId?: number // 需求大厅 ID
  providerUserId?: number // 接单服务商用户ID
  deductPoint?: number // 接单实际扣除的点数，单位：个
  acceptTime?: string | Dayjs // 接单时间
  isValid?: number // 是否有效接单记录：1=有效，0=无效
}

// 服务商接单记录 API
export const ProviderLeadAcceptApi = {
  // 查询服务商接单记录分页
  getProviderLeadAcceptPage: async (params: any) => {
    return await request.get({ url: `/provider/lead-accept/page`, params })
  },

  // 删除服务商接单记录
  deleteProviderLeadAccept: async (id: number) => {
    return await request.delete({ url: `/provider/lead-accept/delete?id=` + id })
  },

  /** 批量删除服务商接单记录 */
  deleteProviderLeadAcceptList: async (ids: number[]) => {
    return await request.delete({
      url: `/provider/lead-accept/delete-list?ids=${ids.join(',')}`
    })
  },
}
