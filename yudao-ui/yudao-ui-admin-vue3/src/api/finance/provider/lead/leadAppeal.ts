import request from '@/config/axios'
import type { Dayjs } from 'dayjs';

/** 服务商线索申诉信息 */
export interface ProviderLeadAppeal {
          id: number; // 主键 ID
          providerLeadId?: number; // 服务商线索 ID
          providerUserId?: number; // 服务商用户 ID
          appealReason?: string; // 申诉原因
          appealEvidence: string; // 申诉证据（JSON 格式）
          appealStatus?: number; // 申诉状态（0=待处理，1=通过，2=驳回）
          appealResultRemark: string; // 申诉处理结果
          handlerUserId: number; // 处理人用户 ID（后台管理员）
          handleTime: string | Dayjs; // 处理时间
  }

// 服务商线索申诉 API
export const ProviderLeadAppealApi = {
  // 查询服务商线索申诉分页
  getProviderLeadAppealPage: async (params: any) => {
    return await request.get({ url: `/provider/lead-appeal/page`, params })
  },

  // 查询服务商线索申诉详情
  getProviderLeadAppeal: async (id: number) => {
    return await request.get({ url: `/provider/lead-appeal/get?id=` + id })
  },

  // 删除服务商线索申诉
  deleteProviderLeadAppeal: async (id: number) => {
    return await request.delete({ url: `/provider/lead-appeal/delete?id=` + id })
  },

  /** 批量删除服务商线索申诉 */
  deleteProviderLeadAppealList: async (ids: number[]) => {
    return await request.delete({ url: `/provider/lead-appeal/delete-list?ids=${ids.join(',')}` })
  }
}