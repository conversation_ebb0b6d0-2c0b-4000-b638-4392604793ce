import request from '@/config/axios'

// 需求大厅 VO
export interface HallVO {
  id: number // 大厅编号
  userId: number // 用户编号
  originType: number // 来源类型
  originSubtype: number // 主动发布子类型
  qualificationTradeType: number // 资质服务交易类型
  demandId: number // 需求编号
  demandType: number // 需求类型
  deductedPoints: number // 接单需要扣除的点数
  sex: number // 用户性别
  contactTitle: string // 用户称呼，如张先生、李女士
  contactMobile: string // 联系方式
  serviceCategoryId: number // 办理业务编号
  industryCategoryId: number // 行业分类编号
  qualificationCategoryId: number // 资质类型编号
  declarationCategoryId: number // 申报类型编号
  subjectCategoryId: number // 主体类型编号
  budgetMin: number // 预算下限，单位：分
  budgetMax: number // 预算上限，单位：分
  remark: string // 用户备注
  sourceAppid: string // 来源应用编码
  sourcePlatform: number // 来源平台：1=系统，2=APP，3=PC，4=微信小程序，5=百度小程序，6=抖音小程序，7=快手小程序
  provinceCode: number // 省编码
  cityCode: number // 市编码
  districtCode: number // 区编码
  status: number // 大厅状态：1-有效，2-已关闭，3-已完成，4-已过期
  acceptedCount: number // 当前已被商家接单数量
  acceptLimit: number // 允许最大接单数，0表示无限制
  isDemo: boolean // 是否为演示单
}

// 需求大厅 API
export const HallApi = {
  // 查询需求大厅分页
  getHallPage: async (params: any) => {
    return await request.get({ url: `/demand/hall/page`, params })
  },

  // 查询需求大厅详情
  getHall: async (id: number) => {
    return await request.get({ url: `/demand/hall/get?id=` + id })
  },

  // 新增需求大厅
  createHall: async (data: HallVO) => {
    return await request.post({ url: `/demand/hall/create`, data })
  },

  // 修改需求大厅
  updateHall: async (data: HallVO) => {
    return await request.put({ url: `/demand/hall/update`, data })
  },

  // 删除需求大厅
  deleteHall: async (id: number) => {
    return await request.delete({ url: `/demand/hall/delete?id=` + id })
  },

  // 导出需求大厅 Excel
  exportHall: async (params) => {
    return await request.download({ url: `/demand/hall/export-excel`, params })
  }
}