import request from '@/config/axios'

// 需求配置 VO
export interface DemandConfigVO {
  publishLimitCount: number // 发布需求次数上限（单个用户在周期内最多可发布的需求数量）
  publishLimitDays: number // 发布限制的周期（单位：天）
  merchantAppointLimitCount: number // 预约商家次数上限（单个用户在周期内最多可预约同一商家的次数）
  merchantAppointLimitDays: number // 商家预约限制周期（单位：天）
  productAppointLimitCount: number // 预约产品次数上限（单个用户在周期内最多可预约同一产品的次数）
  productAppointLimitDays: number // 产品预约限制周期（单位：天）
  auditRegisterMode: number // 注册线索的审核方式（0=无需审核，1=敏感词，2=内容安全，3=人工审核
  auditPublishMode: number // 发布需求的审核方式（0=无需审核，1=敏感词，2=内容安全，3=人工审核
  auditMerchantAppointMode: number // 预约商家的审核方式（0=无需审核，1=敏感词，2=内容安全，3=人工审核）
  auditProductAppointMode: number // 预约产品的审核方式（0=无需审核，1=敏感词，2=内容安全，3=人工审核）
  leadPublishDelayMinutes: number // 线索单延迟进入大厅时间（单位：分钟，0 表示立即展示）
  acceptNeedPoints: number // 商家接单所需点数（单位：点）
  maxAcceptCount: number // 最大可接单的商家数量（0 表示不限制）
  userAppointProviderToHall: boolean // 预约服务商的需求是否展示到线索大厅（0=否，1=是)
  userAppointProductToHall: boolean // 预约产品的需求是否展示到线索大厅（0=否，1=是）
  providerLeadAutoPublishAfterDays: number // 预约服务商线索，服务商N天内未查看则自动发布到大厅（0表示不自动发布）
  productLeadAutoPublishAfterDays: number // 预约产品线索，服务商N天内未查看则自动发布到大厅（0表示不自动发布）
}

// 需求配置 API
export const DemandConfigApi = {
  // 获取需求配置
  getDemandConfig: async () => {
    return await request.get({ url: `/demand/config/get` })
  },

  // 保存需求配置
  saveDemandConfig: async (data: DemandConfigVO) => {
    return await request.put({ url: `/demand/config/save`, data })
  }
}
