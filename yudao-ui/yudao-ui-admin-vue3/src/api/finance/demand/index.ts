import request from '@/config/axios'

// 用户需求 VO
export interface DemandVO {
  id: number // 需求编号
  originType: number // 来源类型：1=预约产品，2=预约商家，3=主动发布
  originSubtype: number // 主动发布子类型：1=发布需求，2=资质服务，3=申报服务，仅 origin_type=3 时有效
  qualificationTradeType: number // 资质服务交易类型：1=资质求购，2=资质转让，仅 origin_subtype=2 时有效
  userId: number // 用户编号
  providerUserId: number // 预约的服务商用户编号，仅预约商家/产品场景使用
  productSpuId: number // 预约的产品SPU编号，仅预约产品场景使用
  sex: number // 用户性别
  contactTitle: string // 用户称呼，如张先生、李女士
  contactMobile: string // 联系方式，用户提交时填写
  serviceCategoryId: number // 办理业务编号，仅发布需求时使用
  industryCategoryId: number // 行业分类编号，仅发布资质服务时使用
  qualificationCategoryId: number // 资质类型编号，仅发布资质服务时使用
  declarationCategoryId: number // 申报类型编号，仅发布申报服务时使用
  subjectCategoryId: number // 主体类型编号，仅发布申报服务时使用
  budgetMin: number // 预算下限，单位：分
  budgetMax: number // 预算上限，单位：分
  remark: string // 用户备注
  provinceCode: number // 省编码
  cityCode: number // 市编码
  districtCode: number // 区编码
  sourcePlatform: number // 来源平台：1=系统，2=APP，3=PC，4=微信小程序，5=百度小程序，6=抖音小程序，7=快手小程序
  sourceAppid: string // 来源应用编码
  auditStatus: number // 审核状态：0=待审核，1=审核通过，2=审核拒绝
  auditTime: Date // 审核时间
  auditReason: string // 审核失败原因
  status: number // 需求状态：1=新建，2=已发布，3=已关闭，4=已完成
}

// 需求信息审核 VO
export interface DemandAuditVO {
  id: number // 审核记录编号
  auditStatus: number // 审核状态：0：待审核，1：审核通过，2：审核拒绝
  auditReason: string // 审核失败原因
}

// 用户需求 API
export const DemandApi = {
  // 查询用户需求分页
  getDemandPage: async (params: any) => {
    return await request.get({ url: `/demand/demand/page`, params })
  },

  // 查询用户需求详情
  getDemand: async (id: number) => {
    return await request.get({ url: `/demand/demand/get?id=` + id })
  },

  // 新增用户需求
  createDemand: async (data: DemandVO) => {
    return await request.post({ url: `/demand/demand/create`, data })
  },

  // 修改用户需求
  updateDemand: async (data: DemandVO) => {
    return await request.put({ url: `/demand/demand/update`, data })
  },

  // 审核用户需求
  auditDemand: async (data: DemandAuditVO) => {
    return await request.put({ url: `/demand/demand/audit`, data })
  },

  // 删除用户需求
  deleteDemand: async (id: number) => {
    return await request.delete({ url: `/demand/demand/delete?id=` + id })
  },

  // 导出用户需求 Excel
  exportDemand: async (params) => {
    return await request.download({ url: `/demand/demand/export-excel`, params })
  }
}
