import request from '@/config/axios'

// 分类展示配置 VO
export interface CategoryDisplayConfigVO {
  id: number // 主键ID
  ownerType: number // 所属类型：10=普通分类，20=聚合分类
  ownerId: number // 所属编号（关联分类或聚合分类 ID）
  parentOwnerId: number // 所属对象父级编号（关联所属分类或所属聚合分类父级 ID）
  displayName: string // 展示名称
  displayScene: number // 展示场景：10=APP首页，20=客源大厅
  mediaId: number // 媒体编号
  mediaUrl: string // 媒体地址
  sort: number // 排序
  status: number // 状态
}

// 分类展示配置 API
export const CategoryDisplayConfigApi = {
  // 查询分类展示配置分页
  getCategoryDisplayConfigPage: async (params: any) => {
    return await request.get({ url: `/category/display-config/page`, params })
  },

  // 查询分类展示配置详情
  getCategoryDisplayConfig: async (id: number) => {
    return await request.get({ url: `/category/display-config/get?id=` + id })
  },

  // 新增分类展示配置
  createCategoryDisplayConfig: async (data: CategoryDisplayConfigVO) => {
    return await request.post({ url: `/category/display-config/create`, data })
  },

  // 修改分类展示配置
  updateCategoryDisplayConfig: async (data: CategoryDisplayConfigVO) => {
    return await request.put({ url: `/category/display-config/update`, data })
  },

  // 删除分类展示配置
  deleteCategoryDisplayConfig: async (id: number) => {
    return await request.delete({ url: `/category/display-config/delete?id=` + id })
  },

  // 导出分类展示配置 Excel
  exportCategoryDisplayConfig: async (params) => {
    return await request.download({ url: `/category/display-config/export-excel`, params })
  }
}
