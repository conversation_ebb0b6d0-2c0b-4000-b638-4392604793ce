import request from '@/config/axios'

// 通用分类项 VO
export interface CategoryVO {
  id: number // 主键编号
  name: string // 分类名称
  shortName: string // 分类简称
  type: number // 分类类型
  parentId: number // 父分类编号
  sort: number // 排序值
  status: number // 状态
  remark: string // 备注
}

// 通用分类项 API
export const CategoryApi = {
  // 查询通用分类项列表
  getCategoryList: async (params) => {
    return await request.get({ url: `/category/category/list`, params })
  },

  // 查询通用分类项详情
  getCategory: async (id: number) => {
    return await request.get({ url: `/category/category/get?id=` + id })
  },

  // 新增通用分类项
  createCategory: async (data: CategoryVO) => {
    return await request.post({ url: `/category/category/create`, data })
  },

  // 修改通用分类项
  updateCategory: async (data: CategoryVO) => {
    return await request.put({ url: `/category/category/update`, data })
  },

  // 删除通用分类项
  deleteCategory: async (id: number) => {
    return await request.delete({ url: `/category/category/delete?id=` + id })
  },

  // 导出通用分类项 Excel
  exportCategory: async (params) => {
    return await request.download({ url: `/category/category/export-excel`, params })
  },

  // 获取分类精简信息列表
  getSimpleCategoryList: async (params) => {
    return request.get({ url: '/category/category/simple-list', params })
  }
}
