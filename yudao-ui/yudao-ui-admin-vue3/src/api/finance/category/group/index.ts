import request from '@/config/axios'

// 分类分组 VO
export interface CategoryGroupVO {
  id: number // 主键编号
  name: string // 聚合分组名称
  code: string // 聚合分组编码
  categoryIds: number[] // 分类编号数组
  sort: number // 排序值
  remark: string // 备注
  status: number // 状态
}

// 分类分组 API
export const CategoryGroupApi = {
  // 查询分类分组列表
  getCategoryGroupList: async (params: any) => {
    return await request.get({ url: `/category/group/list`, params })
  },

  // 查询分类分组详情
  getCategoryGroup: async (id: number) => {
    return await request.get({ url: `/category/group/get?id=` + id })
  },

  // 新增分类分组
  createCategoryGroup: async (data: CategoryGroupVO) => {
    return await request.post({ url: `/category/group/create`, data })
  },

  // 修改分类分组
  updateCategoryGroup: async (data: CategoryGroupVO) => {
    return await request.put({ url: `/category/group/update`, data })
  },

  // 删除分类分组
  deleteCategoryGroup: async (id: number) => {
    return await request.delete({ url: `/category/group/delete?id=` + id })
  },

  // 导出分类分组 Excel
  exportCategoryGroup: async (params) => {
    return await request.download({ url: `/category/group/export-excel`, params })
  }
}
