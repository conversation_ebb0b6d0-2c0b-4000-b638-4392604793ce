import request from '@/config/axios'

// 分类媒体资源 VO
export interface CategoryMediaVO {
  id: number // 主键编号
  ownerType: number // 所属类型
  ownerId: number // 所属编号
  ownerName: number // 所属分类名称
  mediaType: number // 媒体类型
  mediaUrl: string // 资源URL
  sort: number // 排序
  status: number // 状态
}

// 分类媒体资源 API
export const CategoryMediaApi = {
  // 查询分类媒体资源分页
  getCategoryMediaPage: async (params: any) => {
    return await request.get({ url: `/category/media/page`, params })
  },

  // 查询分类媒体资源列表
  getCategoryMediaList: async (params: any) => {
    return await request.get({ url: `/category/media/list`, params })
  },

  // 查询分类媒体资源详情
  getCategoryMedia: async (id: number) => {
    return await request.get({ url: `/category/media/get?id=` + id })
  },

  // 新增分类媒体资源
  createCategoryMedia: async (data: CategoryMediaVO) => {
    return await request.post({ url: `/category/media/create`, data })
  },

  // 修改分类媒体资源
  updateCategoryMedia: async (data: CategoryMediaVO) => {
    return await request.put({ url: `/category/media/update`, data })
  },

  // 删除分类媒体资源
  deleteCategoryMedia: async (id: number) => {
    return await request.delete({ url: `/category/media/delete?id=` + id })
  },

  // 导出分类媒体资源 Excel
  exportCategoryMedia: async (params) => {
    return await request.download({ url: `/category/media/export-excel`, params })
  }
}
