<template>
  <Dialog title="服务商置顶" v-model="dialogVisible">
    <el-form
ref="formRef" :model="formData" :rules="formRules" size="large" label-width="100px"
      v-loading="formLoading">
      <el-form-item label="服务商" prop="mobile">
        <el-input :value="formData.mobile" placeholder="服务商手机" disabled />
      </el-form-item>
      <el-form-item label="置顶类型" prop="pinType">
        <el-radio-group v-model="formData.pinType">
          <el-radio v-for="dict in getIntDictOptions(DICT_TYPE.PIN_TYPE)" :key="dict.value" :value="dict.value" border>
            {{ dict.label }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="置顶套餐" prop="packageId">
        <el-select v-model="formData.packageId" filterable placeholder="请选择置顶套餐">
          <el-option v-for="item in pinPackageOptions" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="置顶城市" prop="pinCityCodes" v-if="formData.pinType === PinTypeEnum.CITY">
        <el-cascader
v-model="formData.pinCityCodes" :options="areaList" :props="{ ...defaultProps, multiple: true }"
          class="w-1/1" clearable filterable placeholder="请选择置顶城市" /> </el-form-item>

    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { PinBizTypeEnum, PinTypeEnum } from '@/utils/constants'
import { defaultProps, limitTreeLevel } from '@/utils/tree'
import { ProviderProfileApi, ProviderProfileVO } from '@/api/member/provider/profile'
import { PinPackageApi, PinPackageVO } from '@/api/finance/pin/pinPackage'
import { PinItemApi } from '@/api/finance/pin/pinItem'
import * as AreaApi from '@/api/system/area'


/** 产品置顶 表单 */
defineOptions({ name: 'ProviderPinForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const formLoading = ref(false) // 表单的加载中：1）提交的按钮禁用
const formData = ref({
  mobile: '',
  userId: undefined,
  pinType: undefined,
  bizType: PinBizTypeEnum.PROVIDER,
  bizId: undefined,
  packageId: undefined,
  pinCityCodes: undefined,
})
const formRules = reactive({
  pinType: [{ required: true, message: '置顶类型不能为空', trigger: 'blur' }],
  packageId: [{ required: true, message: '置顶套餐不能为空', trigger: 'blur' }],
})
const formRef = ref() // 表单 Ref
const areaList = ref([]) // 地区列表
const pinPackageOptions = ref<PinPackageVO[]>([]) // 置顶套餐列表

/** 打开弹窗 */
const open = async (id?: number) => {
  dialogVisible.value = true
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      const res = await ProviderProfileApi.getProviderProfile(id)
      formData.value.mobile = res.mobile
      formData.value.userId = res.userId
      formData.value.bizId = res.userId
    } finally {
      formLoading.value = false
    }
  }
  // 获得地区列表
  const areas = await AreaApi.getAreaTree()
  areaList.value = limitTreeLevel(areas)
  // 获得置顶套餐
  pinPackageOptions.value = await PinPackageApi.getPinPackageSimpleList()
}
defineExpose({ open })

/** 提交表单 */
const emit = defineEmits(['success'])
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown
    await PinItemApi.createPinItem(data)
    message.success(t('common.createSuccess'))
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    mobile: '',
    userId: undefined,
    pinType: undefined,
    bizType: PinBizTypeEnum.PROVIDER,
    bizId: undefined,
    packageId: undefined,
    pinCityCodes: undefined,
  }
  formRef.value?.resetFields()
}

</script>
