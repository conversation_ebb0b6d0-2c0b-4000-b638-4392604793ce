<template>
<template>
  <div class="w-full h-full p-4">
    <div class="mb-6">
      <h3 class="text-lg font-semibold text-gray-800 m-0">应用设置</h3>
    </div>

    <div class="space-y-2">
      <div
        v-for="item in menuList"
        :key="item.type"
        :class="[
          'menu-item cursor-pointer px-4 py-3 rounded-lg transition-all duration-200',
          {
            'bg-blue-50 text-blue-600 border-l-4 border-blue-500': item.type === activeMenuType,
            'hover:bg-gray-50': item.type !== activeMenuType
          }
        ]"
        @click="handleMenuClick(item)"
      >
        <div class="flex items-center">
          <el-icon class="mr-3 text-lg">
            <Document />
          </el-icon>
          <span class="font-medium">{{ item.name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, toRefs, onMounted } from 'vue'
import { Document } from '@element-plus/icons-vue'
import { ClientAppSettingMenu } from '../constants'

defineOptions({ name: 'SettingMenu' })

// 定义属性
const activeMenuType = ref<number | null>(null) // 选中的对话，默认为 null
const hoverMenuType = ref<number | null>(null) // 悬浮上去的选项
const loading = ref<boolean>(false) // 加载中
const menuList = [
  ClientAppSettingMenu.GENERAL,
  ClientAppSettingMenu.PROTOCOL,
  ClientAppSettingMenu.CONTENT,
  ClientAppSettingMenu.TERMINAL,
  ClientAppSettingMenu.VERSION
] // 菜单列表

// 定义组件 props
const props = defineProps<{
  activeType: number | null
}>()

// 定义钩子
const emits = defineEmits([
  'onMenuClick',
])


/** 监听选中的菜单选项 */
const { activeType } = toRefs(props)
watch(activeType, (newValue) => {
  activeMenuType.value = newValue as number
}, { immediate: true })



/** 点击菜单 */
const handleMenuClick = async (option: any) => {
  emits('onMenuClick', option)
  activeMenuType.value = option.type
}
</script>
