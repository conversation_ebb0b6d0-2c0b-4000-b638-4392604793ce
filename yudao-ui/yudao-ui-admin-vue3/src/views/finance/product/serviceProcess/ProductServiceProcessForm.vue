<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
ref="formRef" :model="formData" :rules="formRules" label-width="100px" size="large"
      v-loading="formLoading">
      <el-form-item label="服务类型" prop="serviceCategoryId">
        <!-- 此处暂时使用一级分类看 -->
           <el-select v-model="formData.serviceCategoryId" placeholder="请选择服务类型">
          <el-option v-for="item in categoryTree" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
        <!-- <el-cascader
v-model="formData.serviceCategoryId" :options="categoryTree" :props="defaultProps" class="w-1/1"
          clearable filterabl placeholder="请选择服务类型" /> -->
      </el-form-item>
      <el-form-item label="步骤名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入步骤名称" />
      </el-form-item>

      <el-form-item label="排序值" prop="sort">
        <el-input v-model="formData.sort" placeholder="请输入排序值" />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)" :key="dict.value" :label="dict.value">
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getIntDictOptions, DICT_TYPE } from '@/utils/dict'
import { CategoryTypeEnum, CommonStatusEnum } from '@/utils/constants'
import { defaultProps, handleTree } from '@/utils/tree'
import { CategoryApi } from '@/api/finance/category/category'
import { ProductServiceProcessApi, ProductServiceProcess } from '@/api/finance/product/serviceProcess'

/** 产品服务流程 表单 */
defineOptions({ name: 'ProductServiceProcessForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: undefined,
  serviceCategoryId: undefined,
  sort: 0,
  status: CommonStatusEnum.ENABLE
})
const formRules = reactive({
  name: [{ required: true, message: '步骤名称不能为空', trigger: 'blur' }],
  serviceCategoryId: [{ required: true, message: '服务类型编号不能为空', trigger: 'blur' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref
const categoryTree = ref() // 分类树形结构

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ProductServiceProcessApi.getProductServiceProcess(id)
    } finally {
      formLoading.value = false
    }
  }
  // 获得服务分类
  const categories = await CategoryApi.getCategoryList({ type: CategoryTypeEnum.SERVICE.type })
  categoryTree.value = handleTree(categories, 'id', 'parentId')
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ProductServiceProcess
    if (formType.value === 'create') {
      await ProductServiceProcessApi.createProductServiceProcess(data)
      message.success(t('common.createSuccess'))
    } else {
      await ProductServiceProcessApi.updateProductServiceProcess(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: undefined,
    serviceCategoryId: undefined,
    sort: 0,
    status: CommonStatusEnum.ENABLE
  }
  formRef.value?.resetFields()
}
</script>